// 卡牌管理器
export default class CardManager {
  constructor(scene) {
    this.scene = scene
    this.cardSprites = new Map() // 存储卡牌精灵
    this.selectedCards = [] // 选中的卡牌
    // 调整卡牌尺寸为更合适的大小 - 增大以便更容易看到
    this.cardWidth = 80
    this.cardHeight = 110

    // 调试和监控相关
    this.debugMode = true // 开启调试模式
    this.errorCount = 0 // 错误计数
    this.cardCreationCount = 0 // 卡牌创建计数
    this.lastErrorTime = 0 // 最后一次错误时间
    this.errorHistory = [] // 错误历史记录
  }

  // 记录错误信息
  logError(error, context = '', additionalData = {}) {
    this.errorCount++
    const errorInfo = {
      timestamp: Date.now(),
      error: error.toString(),
      context,
      additionalData,
      stack: error.stack || new Error().stack
    }

    this.errorHistory.push(errorInfo)

    // 保持错误历史记录在合理范围内
    if (this.errorHistory.length > 50) {
      this.errorHistory.shift()
    }

    this.lastErrorTime = Date.now()

    if (this.debugMode) {
      console.error(`[CardManager Error #${this.errorCount}] ${context}:`, error)
      console.error('Additional data:', additionalData)
    }
  }

  // 记录调试信息
  logDebug(message, data = {}) {
    if (this.debugMode) {
      console.log(`[CardManager Debug] ${message}`, data)
    }
  }

  // 获取监控统计信息
  getMonitoringStats() {
    return {
      errorCount: this.errorCount,
      cardCreationCount: this.cardCreationCount,
      lastErrorTime: this.lastErrorTime,
      errorHistory: [...this.errorHistory],
      cardSpritesCount: this.cardSprites.size,
      selectedCardsCount: this.selectedCards.length
    }
  }

  // 获取卡牌图片帧名称
  getCardFrame(card) {
    // 根据plist文件中的命名规则：card_1.png 到 card_54.png
    // 需要将卡牌映射到对应的数字

    if (card.rank === '小王') return 'card_53.png'  // 小王
    if (card.rank === '大王') return 'card_54.png'  // 大王

    // 普通卡牌映射 - 与GameLogic中的顺序保持一致
    const suitOrder = ['♠', '♥', '♣', '♦'] // 黑桃、红心、梅花、方块
    const rankOrder = ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A', '2'] // 与GameLogic一致

    const suitIndex = suitOrder.indexOf(card.suit)
    const rankIndex = rankOrder.indexOf(card.rank)

    if (suitIndex === -1 || rankIndex === -1) {
      console.warn(`Card mapping failed for suit: ${card.suit}, rank: ${card.rank}`)
      return 'card_1.png' // 默认卡牌
    }

    // 计算卡牌编号：每个花色13张牌
    const cardNumber = suitIndex * 13 + rankIndex + 1

    return `card_${cardNumber}.png`
  }

  // 验证卡牌数据的完整性
  validateCardData(card, context = '') {
    if (!card) {
      console.error(`${context}: 卡牌为 null/undefined`)
      return false
    }

    if (typeof card !== 'object') {
      console.error(`${context}: 卡牌不是对象:`, typeof card, card)
      return false
    }

    const isJoker = card.rank === '小王' || card.rank === '大王'

    if (!card.rank) {
      console.error(`${context}: 卡牌缺少 rank:`, card)
      return false
    }

    if (!isJoker && !card.suit) {
      console.error(`${context}: 卡牌缺少 suit:`, card)
      return false
    }

    if (!card.id) {
      console.error(`${context}: 卡牌缺少 id:`, card)
      return false
    }

    if (card.value === undefined || card.value === null) {
      console.error(`${context}: 卡牌缺少 value:`, card)
      return false
    }

    return true
  }

  // 创建错误卡牌显示
  createErrorCard(cardSprite, errorInfo = {}) {
    console.error('=== CREATING ERROR CARD ===')
    console.error('Error info:', errorInfo)
    console.error('Position:', cardSprite.x, cardSprite.y)
    console.error('Stack trace:', new Error().stack)
    console.error('=== END ERROR CARD INFO ===')

    // 创建错误卡牌背景
    const errorCard = this.scene.add.graphics()
    errorCard.fillStyle(0xff4444, 1) // 稍微柔和的红色
    errorCard.fillRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 8)
    errorCard.lineStyle(2, 0x000000, 1)
    errorCard.strokeRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 8)

    // 错误图标
    const errorIcon = this.scene.add.text(0, -10, '⚠️', {
      fontSize: '20px',
      fontFamily: 'Arial, sans-serif'
    }).setOrigin(0.5)

    // 错误文本
    const errorText = this.scene.add.text(0, 10, 'ERROR', {
      fontSize: '10px',
      color: '#ffffff',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5)

    cardSprite.add([errorCard, errorIcon, errorText])
    cardSprite.cardData = errorInfo.card || { id: 'error', rank: 'ERROR', suit: '', value: 0 }
    cardSprite.isSelected = false
    cardSprite.isError = true

    return cardSprite
  }

  // 创建卡牌精灵 - 使用纹理图集中的真实卡牌图片
  createCard(card, x, y, isClickable = false) {
    console.log(`=== 开始创建卡牌 ===`)
    console.log(`卡牌数据:`, card)
    console.log(`位置: (${x}, ${y})`)
    console.log(`可点击: ${isClickable}`)

    let cardSprite
    try {
      this.cardCreationCount++
      cardSprite = this.scene.add.container(x, y)
      const context = `创建卡牌 #${this.cardCreationCount} (${x}, ${y})`

      console.log(`卡牌容器创建成功，编号: ${this.cardCreationCount}`)

      this.logDebug(`开始创建卡牌`, {
        cardId: card?.id,
        position: { x, y },
        isClickable,
        creationCount: this.cardCreationCount
      })

      // 验证卡牌数据
      if (!this.validateCardData(card, context)) {
        this.logError(new Error('卡牌数据验证失败'), context, { card, position: { x, y } })
        return this.createErrorCard(cardSprite, { card, context, position: { x, y } })
      }
    } catch (error) {
      this.logError(error, `创建卡牌容器失败`, { card, position: { x, y } })
      return null
    }

    // 尝试使用纹理图集中的卡牌图片
    const frameName = this.getCardFrame(card)
    let cardImage = null

    try {
      // 检查纹理图集是否加载成功
      if (this.scene.textures.exists('cards') && this.scene.textures.get('cards').has(frameName)) {
        cardImage = this.scene.add.image(0, 0, 'cards', frameName)
        cardImage.setDisplaySize(this.cardWidth, this.cardHeight)
        cardSprite.add(cardImage)

        this.logDebug(`成功使用纹理图集创建卡牌`, {
          cardId: card.id,
          frameName,
          position: { x, y }
        })
      } else {
        this.logDebug(`纹理图集中未找到卡牌帧: ${frameName}，回退到文字渲染`, {
          cardId: card.id,
          texturesExists: this.scene.textures.exists('cards'),
          hasFrame: this.scene.textures.exists('cards') ? this.scene.textures.get('cards').has(frameName) : false
        })
        throw new Error('Frame not found')
      }
    } catch (error) {
      // 如果纹理图集不可用，回退到简单的图形渲染
      this.logDebug(`使用简单图形渲染回退方案`, {
        card,
        error: error.message,
        frameName
      })

      // 创建简单但明显的卡牌背景
      const cardBg = this.scene.add.graphics()

      // 根据花色选择背景颜色
      let bgColor = 0xffffff // 默认白色
      if (card.suit === '♠' || card.suit === '♣') {
        bgColor = 0xf0f0f0 // 浅灰色用于黑色花色
      } else if (card.suit === '♥' || card.suit === '♦') {
        bgColor = 0xfff0f0 // 浅红色用于红色花色
      } else if (card.rank === '小王' || card.rank === '大王') {
        bgColor = card.rank === '大王' ? 0xffd700 : 0xc0c0c0 // 金色大王，银色小王
      }

      // 填充背景
      cardBg.fillStyle(bgColor, 1)
      cardBg.fillRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 8)

      // 粗黑色边框，确保可见
      cardBg.lineStyle(3, 0x000000, 1)
      cardBg.strokeRoundedRect(-this.cardWidth/2, -this.cardHeight/2, this.cardWidth, this.cardHeight, 8)

      // 添加内边框
      cardBg.lineStyle(1, 0x666666, 1)
      cardBg.strokeRoundedRect(-this.cardWidth/2 + 2, -this.cardHeight/2 + 2, this.cardWidth - 4, this.cardHeight - 4, 6)

      // 简化的文字显示 - 只显示中央的牌面信息
      const displayText = card.rank === '小王' || card.rank === '大王' ?
        card.rank : `${card.suit || ''}${card.rank || ''}`

      const centerText = this.scene.add.text(0, 0, displayText, {
        fontSize: '16px',
        color: (card.suit === '♠' || card.suit === '♣') ? '#000000' : '#ff0000',
        fontFamily: 'Arial, sans-serif',
        fontStyle: 'bold',
        stroke: '#ffffff',
        strokeThickness: 1
      }).setOrigin(0.5)

      // 添加卡牌ID作为调试信息（小字）
      const idText = this.scene.add.text(0, this.cardHeight/2 - 10, card.id || 'unknown', {
        fontSize: '8px',
        color: '#666666',
        fontFamily: 'Arial, sans-serif'
      }).setOrigin(0.5)

      cardSprite.add([cardBg, centerText, idText])

      // 确保卡牌可见
      cardSprite.setVisible(true)
      cardSprite.setActive(true)

      this.logDebug(`简单卡牌创建完成`, {
        cardId: card.id,
        displayText,
        bgColor: bgColor.toString(16),
        position: { x: cardSprite.x, y: cardSprite.y },
        visible: cardSprite.visible,
        active: cardSprite.active
      })
    }

    cardSprite.cardData = card
    cardSprite.isSelected = false

    try {
      if (isClickable) {
        cardSprite.setSize(this.cardWidth, this.cardHeight)
        cardSprite.setInteractive()

        cardSprite.on('pointerdown', () => {
          try {
            this.toggleCardSelection(cardSprite)
          } catch (error) {
            this.logError(error, `卡牌点击事件处理失败`, { cardId: card.id })
          }
        })

        cardSprite.on('pointerover', () => {
          try {
            if (!cardSprite.isSelected) {
              cardSprite.setScale(1.05)
              cardSprite.setTint(0xffffcc)
            }
          } catch (error) {
            this.logError(error, `卡牌悬停事件处理失败`, { cardId: card.id })
          }
        })

        cardSprite.on('pointerout', () => {
          try {
            if (!cardSprite.isSelected) {
              cardSprite.setScale(1)
              cardSprite.clearTint()
            }
          } catch (error) {
            this.logError(error, `卡牌离开事件处理失败`, { cardId: card.id })
          }
        })
      }

      this.cardSprites.set(card.id, cardSprite)

      // 最终验证卡牌状态
      console.log(`=== 卡牌创建最终验证 ===`)
      console.log(`卡牌ID: ${card?.id}`)
      console.log(`位置: (${cardSprite.x}, ${cardSprite.y})`)
      console.log(`可见: ${cardSprite.visible}`)
      console.log(`激活: ${cardSprite.active}`)
      console.log(`深度: ${cardSprite.depth}`)
      console.log(`子元素数量: ${cardSprite.list.length}`)
      console.log(`场景中的对象: ${cardSprite.scene ? '是' : '否'}`)

      this.logDebug(`卡牌创建成功`, {
        cardId: card.id,
        position: { x, y },
        isClickable,
        totalSprites: this.cardSprites.size,
        childCount: cardSprite.list.length
      })

      return cardSprite
    } catch (error) {
      this.logError(error, `设置卡牌交互功能失败`, { card, position: { x, y } })
      return cardSprite // 即使交互设置失败，也返回基本的卡牌精灵
    }
  }

  // 切换卡牌选中状态
  toggleCardSelection(cardSprite) {
    if (cardSprite.isSelected) {
      // 取消选中
      cardSprite.isSelected = false
      cardSprite.y += 10
      cardSprite.setScale(1)
      
      const index = this.selectedCards.findIndex(c => c.id === cardSprite.cardData.id)
      if (index !== -1) {
        this.selectedCards.splice(index, 1)
      }
    } else {
      // 选中
      cardSprite.isSelected = true
      cardSprite.y -= 10
      cardSprite.setScale(1.1)
      
      this.selectedCards.push(cardSprite.cardData)
    }
    
    // 触发选中状态变化事件
    this.scene.events.emit('cardsSelectionChanged', this.selectedCards)
  }

  // 清除所有选中状态
  clearSelection() {
    this.cardSprites.forEach(cardSprite => {
      if (cardSprite.isSelected) {
        cardSprite.isSelected = false
        cardSprite.y += 10
        cardSprite.setScale(1)
      }
    })
    this.selectedCards = []
    this.scene.events.emit('cardsSelectionChanged', this.selectedCards)
  }

  // 显示玩家手牌 - 完全垂直排列，无弧形效果
  displayPlayerCards(player, x, y, isCurrentPlayer = false) {
    try {
      // 清除之前的卡牌
      this.clearPlayerCards(player.id)

      const context = `显示玩家 ${player.id} (${player.name}) 手牌`

      // 验证玩家对象
      if (!player) {
        console.error(`${context}: 玩家对象为空`)
        return
      }

      // 调试：检查玩家卡牌数据
      console.log(`=== ${context} ===`)
      console.log('玩家对象:', player)
      console.log('手牌数组:', player.cards)
      console.log('手牌数量:', player.cards ? player.cards.length : 'undefined')
      console.log('isCurrentPlayer:', isCurrentPlayer)

      // 检查每张卡牌的完整性
      if (player.cards && Array.isArray(player.cards)) {
        console.log(`开始检查 ${player.cards.length} 张卡牌...`)
        player.cards.forEach((card, index) => {
          console.log(`检查第 ${index} 张卡牌:`, card)
          if (!this.validateCardData(card, `${context}[${index}]`)) {
            console.error(`第 ${index} 张卡牌验证失败`)
          }
        })
      } else {
        console.error(`${context}: player.cards 不是有效数组:`, player.cards)
      }

      this.debugCardData(player.cards, `Player ${player.id} Cards`)

      // 验证卡牌数组
      if (!player.cards || !Array.isArray(player.cards)) {
        console.error(`${context}: 无效的卡牌数组:`, player.cards)
        return
      }

      // 过滤和验证卡牌
      const validCards = []
      const invalidCards = []

      player.cards.forEach((card, index) => {
        if (this.validateCardData(card, `${context}[${index}]`)) {
          validCards.push(card)
        } else {
          invalidCards.push({ index, card })
        }
      })

      if (invalidCards.length > 0) {
        console.warn(`${context}: 过滤掉了 ${invalidCards.length} 张无效卡牌`)
        console.warn('无效卡牌详情:', invalidCards)
        console.warn('有效卡牌数量:', validCards.length)
      }

      // 如果没有有效卡牌，显示警告
      if (validCards.length === 0) {
        console.warn(`${context}: 没有有效卡牌可显示`)
        return
      }

      // 简化的卡牌间距计算
      const maxWidth = 500 // 最大宽度
      const minSpacing = 20 // 最小间距
      const maxSpacing = 30 // 最大间距

      let cardSpacing = Math.min(maxSpacing, Math.max(minSpacing, maxWidth / validCards.length))
      const totalWidth = (validCards.length - 1) * cardSpacing
      const startX = x - totalWidth / 2

      validCards.forEach((card, index) => {
        try {
          const cardX = startX + index * cardSpacing
          const cardSprite = this.createCard(card, cardX, y, isCurrentPlayer)

          if (cardSprite) {
            cardSprite.playerId = player.id

            // 完全垂直排列 - 移除所有角度和弧形效果
            // 不设置任何旋转或Y轴偏移

            // 设置卡牌深度，确保右边的牌在上层，并且在所有其他元素之上
            cardSprite.setDepth(1000 + index)

            console.log(`卡牌 ${index} 创建成功，位置: (${cardX}, ${y}), 深度: ${1000 + index}`)
            console.log('卡牌精灵:', cardSprite)
          } else {
            console.error(`${context}: 创建第 ${index} 张卡牌失败`, card)
          }
        } catch (error) {
          console.error(`${context}: 创建第 ${index} 张卡牌时发生错误:`, error, card)
        }
      })

    } catch (error) {
      console.error(`显示玩家 ${player?.id} 手牌时发生错误:`, error)
      console.error('玩家数据:', player)
      console.error('位置:', { x, y })
    }
  }

  // 显示其他玩家手牌（背面）
  displayOtherPlayerCards(player, x, y, isVertical = false, position = 'left') {
    this.clearPlayerCards(player.id)

    const cardCount = player.cards.length

    // 根据位置调整卡牌排列
    let spacing, startX, startY, cardWidth, cardHeight

    if (isVertical) {
      // 垂直排列时使用较小的卡牌和间距
      cardWidth = this.cardWidth * 0.7
      cardHeight = this.cardHeight * 0.7
      spacing = 12

      const totalHeight = (cardCount - 1) * spacing
      startY = y - totalHeight / 2
      startX = x

      // 左右两侧的偏移调整
      if (position === 'left') {
        startX = x + cardWidth / 2
      } else if (position === 'right') {
        startX = x - cardWidth / 2
      }
    } else {
      // 水平排列
      cardWidth = this.cardWidth
      cardHeight = this.cardHeight
      spacing = 30

      const totalWidth = (cardCount - 1) * spacing
      startX = x - totalWidth / 2
      startY = y
    }

    for (let i = 0; i < cardCount; i++) {
      const cardX = isVertical ? startX : startX + i * spacing
      const cardY = isVertical ? startY + i * spacing : startY

      const cardSprite = this.scene.add.container(cardX, cardY)

      // 卡牌背面 - 使用更好的蓝色渐变
      const cardBg = this.scene.add.graphics()
      cardBg.fillGradientStyle(0x1e3a8a, 0x1e3a8a, 0x3b82f6, 0x3b82f6, 1)
      cardBg.fillRoundedRect(-cardWidth/2, -cardHeight/2, cardWidth, cardHeight, 6)
      cardBg.lineStyle(2, 0x1e40af, 1)
      cardBg.strokeRoundedRect(-cardWidth/2, -cardHeight/2, cardWidth, cardHeight, 6)

      // 背面图案
      const pattern = this.scene.add.text(0, 0, '♠', {
        fontSize: isVertical ? '20px' : '28px',
        color: '#ffffff',
        fontStyle: 'bold'
      }).setOrigin(0.5)

      // 添加装饰边框
      const innerBorder = this.scene.add.graphics()
      innerBorder.lineStyle(1, 0x60a5fa, 0.8)
      innerBorder.strokeRoundedRect(-cardWidth/2 + 2, -cardHeight/2 + 2,
                                   cardWidth - 4, cardHeight - 4, 4)

      cardSprite.add([cardBg, innerBorder, pattern])
      cardSprite.playerId = player.id

      // 添加轻微的层次效果
      cardSprite.setDepth(i)

      this.cardSprites.set(`back_${player.id}_${i}`, cardSprite)
    }
  }

  // 清除指定玩家的卡牌
  clearPlayerCards(playerId) {
    const toRemove = []
    this.cardSprites.forEach((sprite, key) => {
      if (sprite.playerId === playerId || key.startsWith(`back_${playerId}_`)) {
        sprite.destroy()
        toRemove.push(key)
      }
    })
    toRemove.forEach(key => this.cardSprites.delete(key))
  }

  // 显示出牌区域的卡牌
  displayPlayedCards(cards, x, y) {
    // 清除之前的出牌
    this.clearPlayedCards()

    if (!cards || cards.length === 0) return

    const cardSpacing = 35
    const startX = x - (cards.length * cardSpacing) / 2

    // 添加出牌区域背景
    const playAreaBg = this.scene.add.graphics()
    playAreaBg.fillStyle(0x000000, 0.3)
    playAreaBg.fillRoundedRect(startX - 20, y - this.cardHeight/2 - 10,
                              cards.length * cardSpacing + 20, this.cardHeight + 20, 10)
    playAreaBg.lineStyle(2, 0xffffff, 0.5)
    playAreaBg.strokeRoundedRect(startX - 20, y - this.cardHeight/2 - 10,
                                cards.length * cardSpacing + 20, this.cardHeight + 20, 10)
    this.cardSprites.set('played_bg', playAreaBg)

    cards.forEach((card, index) => {
      const cardX = startX + index * cardSpacing
      const cardSprite = this.createCard(card, cardX, y)
      cardSprite.setScale(0.85)
      // 添加轻微的阴影效果
      cardSprite.setDepth(10 + index)
      this.cardSprites.set(`played_${index}`, cardSprite)
    })
  }

  // 清除出牌区域
  clearPlayedCards() {
    const toRemove = []
    this.cardSprites.forEach((sprite, key) => {
      if (key.startsWith('played_')) {
        sprite.destroy()
        toRemove.push(key)
      }
    })
    toRemove.forEach(key => this.cardSprites.delete(key))
  }

  // 显示地主牌
  displayLandlordCards(cards, x, y) {
    // 清除之前的地主牌
    this.clearLandlordCards()

    if (!cards || cards.length === 0) return

    const cardSpacing = 35
    const startX = x - (cards.length * cardSpacing) / 2

    // 添加地主牌区域标识
    const landlordLabel = this.scene.add.text(x, y - 50, '地主牌', {
      fontSize: '16px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold',
      stroke: '#000000',
      strokeThickness: 2,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      padding: { x: 10, y: 5 }
    }).setOrigin(0.5)
    this.cardSprites.set('landlord_label', landlordLabel)

    // 添加地主牌背景
    const landlordBg = this.scene.add.graphics()
    landlordBg.fillStyle(0xFFD700, 0.2)
    landlordBg.fillRoundedRect(startX - 15, y - this.cardHeight/2 - 5,
                              cards.length * cardSpacing + 10, this.cardHeight + 10, 8)
    landlordBg.lineStyle(2, 0xFFD700, 0.8)
    landlordBg.strokeRoundedRect(startX - 15, y - this.cardHeight/2 - 5,
                                cards.length * cardSpacing + 10, this.cardHeight + 10, 8)
    this.cardSprites.set('landlord_bg', landlordBg)

    cards.forEach((card, index) => {
      const cardX = startX + index * cardSpacing
      const cardSprite = this.createCard(card, cardX, y)
      cardSprite.setScale(0.75)
      cardSprite.setDepth(20 + index)
      this.cardSprites.set(`landlord_${index}`, cardSprite)
    })
  }

  // 清除地主牌
  clearLandlordCards() {
    const toRemove = []
    this.cardSprites.forEach((sprite, key) => {
      if (key.startsWith('landlord_')) {
        sprite.destroy()
        toRemove.push(key)
      }
    })
    toRemove.forEach(key => this.cardSprites.delete(key))
  }

  // 获取选中的卡牌
  getSelectedCards() {
    return [...this.selectedCards]
  }

  // 调试方法：检查卡牌数据
  debugCardData(cards, label = 'Cards') {
    console.log(`=== ${label} Debug ===`)
    if (!cards || cards.length === 0) {
      console.log('No cards to debug')
      return
    }

    cards.forEach((card, index) => {
      console.log(`Card ${index}:`, {
        id: card.id,
        suit: card.suit,
        rank: card.rank,
        value: card.value,
        display: card.display,
        frameName: this.getCardFrame(card)
      })

      // 检查是否有空值（大小王的suit可以为空）
      const isJoker = card.rank === '小王' || card.rank === '大王'
      if ((!isJoker && (!card.suit || !card.rank)) || (isJoker && !card.rank)) {
        console.error(`Card ${index} has missing data!`, card)
      }
    })
    console.log(`=== End ${label} Debug ===`)
  }

  // 显示监控面板（调试用）
  showMonitoringPanel() {
    if (!this.debugMode) return

    const stats = this.getMonitoringStats()
    console.group('🎴 CardManager 监控面板')
    console.log('📊 统计信息:')
    console.log(`  - 错误总数: ${stats.errorCount}`)
    console.log(`  - 卡牌创建总数: ${stats.cardCreationCount}`)
    console.log(`  - 当前卡牌精灵数: ${stats.cardSpritesCount}`)
    console.log(`  - 选中卡牌数: ${stats.selectedCardsCount}`)
    console.log(`  - 最后错误时间: ${stats.lastErrorTime ? new Date(stats.lastErrorTime).toLocaleTimeString() : '无'}`)

    if (stats.errorHistory.length > 0) {
      console.log('🚨 最近错误:')
      stats.errorHistory.slice(-5).forEach((error, index) => {
        console.log(`  ${index + 1}. [${new Date(error.timestamp).toLocaleTimeString()}] ${error.context}: ${error.error}`)
      })
    }
    console.groupEnd()
  }

  // 重置监控统计
  resetMonitoring() {
    this.errorCount = 0
    this.cardCreationCount = 0
    this.lastErrorTime = 0
    this.errorHistory = []
    this.logDebug('监控统计已重置')
  }

  // 销毁所有卡牌
  destroy() {
    try {
      this.logDebug('开始销毁卡牌管理器', {
        cardSpritesCount: this.cardSprites.size,
        selectedCardsCount: this.selectedCards.length
      })

      this.cardSprites.forEach((sprite, key) => {
        try {
          sprite.destroy()
        } catch (error) {
          this.logError(error, `销毁卡牌精灵失败`, { key })
        }
      })

      this.cardSprites.clear()
      this.selectedCards = []

      this.logDebug('卡牌管理器销毁完成')

      // 显示最终监控报告
      if (this.debugMode) {
        this.showMonitoringPanel()
      }
    } catch (error) {
      console.error('销毁卡牌管理器时发生错误:', error)
    }
  }
}
