<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单卡牌测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #2d5a27;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }
        .controls {
            text-align: center;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            background-color: #4CAF50;
            color: white;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .btn.danger {
            background-color: #f44336;
        }
        .btn.danger:hover {
            background-color: #da190b;
        }
        #game-container {
            width: 100%;
            height: 600px;
            border: 2px solid #fff;
            border-radius: 10px;
            margin: 20px 0;
            background-color: #1a4a1a;
        }
        .info {
            color: white;
            text-align: center;
            margin: 10px 0;
        }
        .status {
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: white; text-align: center;">🎴 卡牌错误修复测试</h1>
        
        <div class="controls">
            <button class="btn" onclick="startNewGame()">开始新游戏</button>
            <button class="btn" onclick="dealCards()">重新发牌</button>
            <button class="btn" onclick="showMonitoring()">显示监控 (M)</button>
            <button class="btn" onclick="resetStats()">重置统计 (R)</button>
            <button class="btn danger" onclick="testErrorCard()">测试错误卡牌</button>
        </div>
        
        <div class="info">
            <p>按 M 键显示监控面板 | 按 R 键重置统计 | 观察控制台查看详细日志</p>
        </div>
        
        <div id="game-container"></div>
        
        <div class="status" id="status">
            系统就绪 - 点击"开始新游戏"开始测试
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script type="module">
        // 简化的游戏逻辑用于测试
        class TestGameLogic {
            constructor() {
                this.reset()
            }
            
            reset() {
                this.players = [
                    { id: 0, name: '玩家1', cards: [], isLandlord: false },
                    { id: 1, name: '玩家2', cards: [], isLandlord: false },
                    { id: 2, name: '玩家3', cards: [], isLandlord: false }
                ]
                this.currentPlayer = 0
                this.gamePhase = 'dealing'
            }
            
            generateTestCards() {
                const suits = ['♠', '♥', '♣', '♦']
                const ranks = ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A', '2']
                const cards = []
                
                // 生成一些测试卡牌
                for (let i = 0; i < 17; i++) {
                    const suit = suits[Math.floor(Math.random() * suits.length)]
                    const rank = ranks[Math.floor(Math.random() * ranks.length)]
                    cards.push({
                        id: `${suit}${rank}_${i}`,
                        suit: suit,
                        rank: rank,
                        value: ranks.indexOf(rank) + 3,
                        display: `${suit}${rank}`
                    })
                }
                
                // 添加一些大小王
                if (Math.random() > 0.5) {
                    cards.push({
                        id: 'joker_small',
                        suit: '',
                        rank: '小王',
                        value: 16,
                        display: '🃏'
                    })
                }
                
                if (Math.random() > 0.5) {
                    cards.push({
                        id: 'joker_big',
                        suit: '',
                        rank: '大王',
                        value: 17,
                        display: '🃟'
                    })
                }
                
                return cards
            }
            
            dealCards() {
                this.players[0].cards = this.generateTestCards()
                this.players[1].cards = this.generateTestCards().slice(0, 10)
                this.players[2].cards = this.generateTestCards().slice(0, 12)
                this.gamePhase = 'playing'
            }
            
            getGameState() {
                return {
                    players: JSON.parse(JSON.stringify(this.players)),
                    currentPlayer: this.currentPlayer,
                    gamePhase: this.gamePhase,
                    landlordId: -1
                }
            }
        }
        
        // 导入卡牌管理器
        import CardManager from './src/game/CardManager.js';
        
        let game = null;
        let gameLogic = null;
        let cardManager = null;
        
        class TestScene extends Phaser.Scene {
            constructor() {
                super({ key: 'TestScene' })
            }
            
            preload() {
                // 创建简单的卡牌纹理
                this.load.image('bg2', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==')
            }
            
            create() {
                // 设置背景
                this.add.rectangle(400, 300, 800, 600, 0x2d5a27)
                
                // 初始化游戏逻辑和卡牌管理器
                gameLogic = new TestGameLogic()
                cardManager = new CardManager(this)
                
                // 添加键盘监听
                this.input.keyboard.on('keydown-M', () => {
                    if (cardManager) {
                        cardManager.showMonitoringPanel()
                    }
                })
                
                this.input.keyboard.on('keydown-R', () => {
                    if (cardManager) {
                        cardManager.resetMonitoring()
                        updateStatus('监控统计已重置')
                    }
                })
                
                updateStatus('测试场景已加载')
            }
        }
        
        function initTestGame() {
            const config = {
                type: Phaser.AUTO,
                parent: 'game-container',
                width: 800,
                height: 600,
                scene: TestScene,
                render: {
                    antialias: true,
                    pixelArt: false
                }
            }
            
            game = new Phaser.Game(config)
        }
        
        window.startNewGame = function() {
            if (game) {
                game.destroy(true)
            }
            initTestGame()
            updateStatus('新游戏已开始')
        }
        
        window.dealCards = function() {
            if (!gameLogic || !cardManager) {
                updateStatus('请先开始游戏')
                return
            }
            
            try {
                gameLogic.dealCards()
                const gameState = gameLogic.getGameState()
                
                // 显示玩家手牌
                cardManager.displayPlayerCards(gameState.players[0], 400, 500, true)
                cardManager.displayOtherPlayerCards(gameState.players[1], 100, 300, true, 'left')
                cardManager.displayOtherPlayerCards(gameState.players[2], 700, 300, true, 'right')
                
                updateStatus(`发牌完成 - 玩家1: ${gameState.players[0].cards.length}张`)
            } catch (error) {
                updateStatus('发牌失败: ' + error.message)
                console.error('发牌错误:', error)
            }
        }
        
        window.showMonitoring = function() {
            if (cardManager) {
                cardManager.showMonitoringPanel()
                updateStatus('监控面板已显示在控制台')
            } else {
                updateStatus('卡牌管理器未初始化')
            }
        }
        
        window.resetStats = function() {
            if (cardManager) {
                cardManager.resetMonitoring()
                updateStatus('监控统计已重置')
            } else {
                updateStatus('卡牌管理器未初始化')
            }
        }
        
        window.testErrorCard = function() {
            if (!cardManager) {
                updateStatus('卡牌管理器未初始化')
                return
            }
            
            try {
                // 测试各种错误情况
                const errorCases = [
                    null,
                    undefined,
                    {},
                    { id: 'test1' },
                    { id: 'test2', rank: 'A' },
                    { id: 'test3', suit: '♠' },
                    { id: 'test4', rank: 'K', suit: '♥' }, // 缺少value
                    'invalid_string',
                    123
                ]
                
                errorCases.forEach((errorCard, index) => {
                    const x = 100 + (index % 5) * 80
                    const y = 100 + Math.floor(index / 5) * 100
                    cardManager.createCard(errorCard, x, y, false)
                })
                
                updateStatus('已创建多个错误卡牌用于测试')
            } catch (error) {
                updateStatus('测试错误卡牌失败: ' + error.message)
                console.error('测试错误:', error)
            }
        }
        
        function updateStatus(message) {
            const statusElement = document.getElementById('status')
            const timestamp = new Date().toLocaleTimeString()
            statusElement.textContent = `[${timestamp}] ${message}`
            console.log(`[TestStatus] ${message}`)
        }
        
        // 页面加载完成后自动开始
        window.addEventListener('load', () => {
            setTimeout(startNewGame, 500)
        })
    </script>
</body>
</html>
