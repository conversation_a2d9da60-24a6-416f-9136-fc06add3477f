<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡牌错误修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .controls {
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        #game-container {
            width: 100%;
            height: 600px;
            border: 2px solid #333;
            border-radius: 10px;
            margin: 20px 0;
        }
        .info-panel {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .log-output {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎴 卡牌错误修复测试</h1>
            <p>测试卡牌显示错误的修复效果</p>
        </div>

        <div class="test-section">
            <h3>🎮 游戏测试</h3>
            <div class="controls">
                <button class="btn btn-primary" onclick="startGame()">开始游戏</button>
                <button class="btn btn-success" onclick="restartGame()">重新开始</button>
                <button class="btn btn-warning" onclick="showMonitoring()">显示监控 (M键)</button>
                <button class="btn btn-warning" onclick="resetMonitoring()">重置监控 (R键)</button>
                <button class="btn btn-danger" onclick="simulateError()">模拟错误</button>
            </div>
            <div id="game-container"></div>
        </div>

        <div class="test-section">
            <h3>📊 监控信息</h3>
            <div class="info-panel">
                <div id="status-display">
                    <span class="status success">系统就绪</span>
                </div>
                <p><strong>说明：</strong></p>
                <ul>
                    <li>按 <kbd>M</kbd> 键显示详细监控面板</li>
                    <li>按 <kbd>R</kbd> 键重置监控统计</li>
                    <li>观察控制台输出查看详细日志</li>
                    <li>如果出现红色错误卡牌，说明检测到数据问题</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 测试项目</h3>
            <div class="info-panel">
                <h4>已修复的问题：</h4>
                <ul>
                    <li>✅ 增强了卡牌数据验证机制</li>
                    <li>✅ 改进了错误处理和恢复</li>
                    <li>✅ 添加了详细的调试和监控功能</li>
                    <li>✅ 使用安全的深拷贝替代JSON序列化</li>
                    <li>✅ 增加了错误卡牌的可视化显示</li>
                </ul>
                
                <h4>测试方法：</h4>
                <ol>
                    <li>开始游戏并观察卡牌是否正常显示</li>
                    <li>多次重新开始游戏，检查是否出现错误卡牌</li>
                    <li>使用监控功能查看系统状态</li>
                    <li>模拟错误情况，验证错误处理机制</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 控制台日志</h3>
            <div class="log-output" id="log-output">
                控制台日志将显示在这里...
                按F12打开浏览器开发者工具查看完整日志。
            </div>
        </div>
    </div>

    <script type="module">
        import initGame from './src/game/GameScene.js';
        
        let gameInstance = null;
        
        window.startGame = function() {
            try {
                if (gameInstance) {
                    gameInstance.destroy();
                }
                gameInstance = initGame();
                updateStatus('游戏已启动', 'success');
                logMessage('游戏启动成功');
            } catch (error) {
                updateStatus('游戏启动失败: ' + error.message, 'error');
                logMessage('错误: ' + error.message);
            }
        };
        
        window.restartGame = function() {
            if (gameInstance && gameInstance.scene.scenes[1]) {
                gameInstance.scene.scenes[1].restartGame();
                updateStatus('游戏已重启', 'success');
                logMessage('游戏重启');
            } else {
                updateStatus('请先启动游戏', 'warning');
            }
        };
        
        window.showMonitoring = function() {
            if (gameInstance && gameInstance.scene.scenes[1] && gameInstance.scene.scenes[1].cardManager) {
                gameInstance.scene.scenes[1].cardManager.showMonitoringPanel();
                updateStatus('监控面板已显示在控制台', 'success');
                logMessage('显示监控面板');
            } else {
                updateStatus('游戏未运行', 'warning');
            }
        };
        
        window.resetMonitoring = function() {
            if (gameInstance && gameInstance.scene.scenes[1] && gameInstance.scene.scenes[1].cardManager) {
                gameInstance.scene.scenes[1].cardManager.resetMonitoring();
                updateStatus('监控统计已重置', 'success');
                logMessage('监控统计重置');
            } else {
                updateStatus('游戏未运行', 'warning');
            }
        };
        
        window.simulateError = function() {
            if (gameInstance && gameInstance.scene.scenes[1] && gameInstance.scene.scenes[1].cardManager) {
                // 模拟创建一个错误卡牌
                const cardManager = gameInstance.scene.scenes[1].cardManager;
                const errorCard = cardManager.createCard(null, 400, 300, false);
                updateStatus('已模拟错误卡牌', 'warning');
                logMessage('模拟错误: 创建了一个空卡牌');
            } else {
                updateStatus('游戏未运行', 'warning');
            }
        };
        
        function updateStatus(message, type) {
            const statusDisplay = document.getElementById('status-display');
            statusDisplay.innerHTML = `<span class="status ${type}">${message}</span>`;
        }
        
        function logMessage(message) {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // 拦截控制台输出
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logMessage('LOG: ' + args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logMessage('ERROR: ' + args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logMessage('WARN: ' + args.join(' '));
        };
        
        // 页面加载完成后自动启动游戏
        window.addEventListener('load', () => {
            setTimeout(() => {
                startGame();
            }, 1000);
        });
    </script>
</body>
</html>
